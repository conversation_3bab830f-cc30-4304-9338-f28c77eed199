import re
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, <PERSON>, Iterator, Op<PERSON>, Tu<PERSON>, Union
from dataclasses import dataclass
from enum import Enum
import hashlib
import json
from functools import lru_cache

from modules.connectors.base import BaseConnector
from modules.connectors.handlers.Jira.constants.entities import EntityType, get_all_entity_types
from modules.connectors.handlers.Jira.constants.relationships import RelationshipType, get_all_relationship_types


# Configure logging
logger = logging.getLogger(__name__)

#should be moved to config
@dataclass
class SearchConfig:
    """Configuration for Jira search functionality"""
    max_results: int = 50
    default_limit: int = 10
    cache_ttl_seconds: int = 300  # 5 minutes
    enable_fuzzy_search: bool = True
    fuzzy_threshold: float = 0.8
    enable_caching: bool = True
    query_timeout_seconds: int = 30
    max_query_length: int = 1000
    enable_query_logging: bool = True

#should be moved to config
class QueryType(Enum):
    """Enumeration of supported query types"""
    ISSUE_SEARCH = "issue_search"
    USER_ASSIGNMENT = "user_assignment"
    PROJECT_SEARCH = "project_search"
    RELATIONSHIP_SEARCH = "relationship_search"
    ENTITY_LISTING = "entity_listing"
    GENERIC_SEARCH = "generic_search"
    DATE_FILTERED = "date_filtered"


@dataclass
class ParsedQuery:
    """Structured representation of a parsed search query"""
    query_type: QueryType
    keywords: List[str]
    entity_filters: Dict[str, str]
    date_filter: Optional[datetime]
    relationship_type: Optional[str]
    limit: int
    raw_query: str
    confidence_score: float


class QuerySanitizer:
    """Handles query sanitization and validation"""

    DANGEROUS_PATTERNS = [
        r'[;\'"\\]',  # SQL injection characters
        r'(?i)(drop|delete|update|insert|create|alter|exec|execute)',  # Dangerous SQL keywords
        r'(?i)(script|javascript|vbscript)',  # Script injection
        r'[<>]',  # HTML/XML injection
    ]

    @staticmethod
    def sanitize_input(query: str, config: SearchConfig) -> str:
        """Sanitize and validate input query"""
        if not query or not isinstance(query, str):
            raise ValueError("Query must be a non-empty string")

        if len(query) > config.max_query_length:
            raise ValueError(f"Query length exceeds maximum of {config.max_query_length} characters")

        # Check for dangerous patterns
        for pattern in QuerySanitizer.DANGEROUS_PATTERNS:
            if re.search(pattern, query):
                logger.warning(f"Potentially dangerous pattern detected in query: {query}")
                raise ValueError("Query contains potentially dangerous characters or keywords")

        # Basic sanitization
        sanitized = query.strip()
        sanitized = re.sub(r'\s+', ' ', sanitized)  # Normalize whitespace

        return sanitized

    @staticmethod
    def escape_cypher_string(value: str) -> str:
        """Safely escape strings for Cypher queries"""
        if not isinstance(value, str):
            return str(value)

        # Escape single quotes and backslashes
        escaped = value.replace('\\', '\\\\').replace("'", "\\'")
        return escaped


class AdvancedQueryParser:
    """Advanced natural language query parser for Jira search"""

    # Query pattern definitions
    QUERY_PATTERNS = {
        QueryType.ISSUE_SEARCH: [
            r'(?i)(find|search|show|get|list)\s+(issues?|tickets?)\s+(?:with|containing|about)\s+(.+)',
            r'(?i)(issues?|tickets?)\s+(?:with|containing|about)\s+(.+)',
            r'(?i)(.+)\s+(issues?|tickets?)',
        ],
        QueryType.USER_ASSIGNMENT: [
            r'(?i)(issues?|tickets?)\s+(?:assigned\s+to|by\s+user)\s+["\']?([^"\']+)["\']?',
            r'(?i)(?:assigned\s+to|by\s+user)\s+["\']?([^"\']+)["\']?\s+(issues?|tickets?)',
            r'(?i)what\s+(?:did|has)\s+([^"\']+)\s+(?:do|work\s+on)',
        ],
        QueryType.PROJECT_SEARCH: [
            r'(?i)(?:in\s+project|project)\s+["\']?([^"\']+)["\']?',
            r'(?i)(issues?|tickets?)\s+(?:in|from)\s+(?:project\s+)?["\']?([^"\']+)["\']?',
        ],
        QueryType.RELATIONSHIP_SEARCH: [
            r'(?i)(?:what|which)\s+(?:issues?|tickets?)\s+(?:are\s+)?(blocking|blocked\s+by|depends?\s+on|relates?\s+to)\s+([A-Z]+-\d+)',
            r'(?i)([A-Z]+-\d+)\s+(blocks?|depends?\s+on|relates?\s+to)',
        ],
        QueryType.ENTITY_LISTING: [
            r'(?i)(?:find|show|list|get)\s+(?:all\s+)?(users?|projects?|components?|sprints?|versions?)',
            r'(?i)(users?|projects?|components?|sprints?|versions?)$',
        ],
    }

    # Synonyms for better matching
    SYNONYMS = {
        'issue': ['ticket', 'task', 'story', 'bug', 'feature'],
        'assigned': ['given', 'allocated', 'designated'],
        'project': ['proj', 'workspace'],
        'user': ['person', 'developer', 'assignee'],
        'blocking': ['blocks', 'blocked by'],
        'related': ['relates to', 'connected to', 'linked to'],
    }

    # Date patterns
    DATE_PATTERNS = {
        'yesterday': lambda: datetime.now() - timedelta(days=1),
        'today': lambda: datetime.now(),
        'this week': lambda: datetime.now() - timedelta(days=7),
        'last week': lambda: datetime.now() - timedelta(days=14),
        'this month': lambda: datetime.now() - timedelta(days=30),
    }

    @classmethod
    def parse_query(cls, query: str, config: SearchConfig) -> ParsedQuery:
        """Parse natural language query into structured format"""
        sanitized_query = QuerySanitizer.sanitize_input(query, config)
        query_lower = sanitized_query.lower()

        # Extract date information
        date_filter = cls._extract_date_filter(query_lower)

        # Determine query type and extract relevant information
        query_type, entity_filters, keywords, relationship_type, confidence = cls._classify_query(query_lower)

        # Determine appropriate limit
        limit = cls._extract_limit(query_lower, config.default_limit)

        return ParsedQuery(
            query_type=query_type,
            keywords=keywords,
            entity_filters=entity_filters,
            date_filter=date_filter,
            relationship_type=relationship_type,
            limit=limit,
            raw_query=sanitized_query,
            confidence_score=confidence
        )

    @classmethod
    def _extract_date_filter(cls, query: str) -> Optional[datetime]:
        """Extract date filter from query"""
        # Check for relative dates
        for date_phrase, date_func in cls.DATE_PATTERNS.items():
            if date_phrase in query:
                return date_func()

        # Check for specific dates (YYYY-MM-DD format)
        date_match = re.search(r'\b(\d{4}-\d{2}-\d{2})\b', query)
        if date_match:
            try:
                return datetime.strptime(date_match.group(1), "%Y-%m-%d")
            except ValueError:
                logger.warning(f"Invalid date format: {date_match.group(1)}")

        return None

    @classmethod
    def _classify_query(cls, query: str) -> Tuple[QueryType, Dict[str, str], List[str], Optional[str], float]:
        """Classify query type and extract relevant information"""
        best_match = None
        best_confidence = 0.0
        best_type = QueryType.GENERIC_SEARCH

        for query_type, patterns in cls.QUERY_PATTERNS.items():
            for pattern in patterns:
                match = re.search(pattern, query)
                if match:
                    confidence = len(match.group(0)) / len(query)  # Simple confidence based on match length
                    if confidence > best_confidence:
                        best_match = match
                        best_confidence = confidence
                        best_type = query_type

        # Extract information based on best match
        entity_filters = {}
        keywords = []
        relationship_type = None

        if best_match and best_type != QueryType.GENERIC_SEARCH:
            if best_type == QueryType.ISSUE_SEARCH:
                keywords = cls._extract_keywords(best_match.groups()[-1] if best_match.groups() else query)
            elif best_type == QueryType.USER_ASSIGNMENT:
                entity_filters['user'] = best_match.group(1) if best_match.groups() else ""
            elif best_type == QueryType.PROJECT_SEARCH:
                entity_filters['project'] = best_match.group(1) if best_match.groups() else ""
            elif best_type == QueryType.RELATIONSHIP_SEARCH:
                if len(best_match.groups()) >= 2:
                    relationship_type = best_match.group(1)
                    entity_filters['issue_id'] = best_match.group(2)
            elif best_type == QueryType.ENTITY_LISTING:
                entity_filters['entity_type'] = best_match.group(1) if best_match.groups() else ""
        else:
            # Fallback to generic keyword extraction
            keywords = cls._extract_keywords(query)

        return best_type, entity_filters, keywords, relationship_type, best_confidence

    @classmethod
    def _extract_keywords(cls, text: str) -> List[str]:
        """Extract meaningful keywords from text"""
        if not text:
            return []

        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'find', 'search', 'show', 'get', 'list',
            'issue', 'issues', 'ticket', 'tickets', 'yesterday', 'today', 'on'
        }

        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]

        return keywords[:5]  # Limit to top 5 keywords

    @classmethod
    def _extract_limit(cls, query: str, default_limit: int) -> int:
        """Extract result limit from query"""
        limit_match = re.search(r'\b(?:limit|top|first)\s+(\d+)\b', query)
        if limit_match:
            try:
                return min(int(limit_match.group(1)), 100)  # Cap at 100
            except ValueError:
                pass

        return default_limit


class SecureCypherBuilder:
    """Builds secure, parameterized Cypher queries"""

    @staticmethod
    def build_issue_search_query(keywords: List[str], date_filter: Optional[datetime], limit: int) -> Tuple[str, Dict[str, Any]]:
        """Build secure query for issue search"""
        params = {'limit': limit}
        where_clauses = []

        if keywords:
            keyword_conditions = []
            for i, keyword in enumerate(keywords[:3]):  # Limit to 3 keywords
                param_name = f'keyword_{i}'
                params[param_name] = keyword
                keyword_conditions.append(f"(i.summary CONTAINS ${param_name} OR i.description CONTAINS ${param_name})")
            where_clauses.append(f"({' OR '.join(keyword_conditions)})")

        if date_filter:
            params['target_date'] = date_filter.date().isoformat()
            where_clauses.append("date(i.updated_at) = date($target_date)")

        where_clause = " AND ".join(where_clauses) if where_clauses else "true"

        query = f"""
        MATCH (i:JiraIssue)
        WHERE {where_clause}
        RETURN i.id AS id, i.summary AS summary, i.description AS description,
               labels(i) AS labels, i.updated_at AS updated_at
        ORDER BY i.updated_at DESC
        LIMIT $limit
        """

        return query.strip(), params

    @staticmethod
    def build_user_assignment_query(user_name: str, date_filter: Optional[datetime], limit: int) -> Tuple[str, Dict[str, Any]]:
        """Build secure query for user assignment search"""
        params = {
            'user_name': user_name,
            'limit': limit
        }

        where_clauses = ["u.displayName CONTAINS $user_name"]

        if date_filter:
            params['target_date'] = date_filter.date().isoformat()
            where_clauses.append("date(i.updated_at) = date($target_date)")

        where_clause = " AND ".join(where_clauses)

        query = f"""
        MATCH (u:JiraUser)-[:ASSIGNED_TO]->(i:JiraIssue)
        WHERE {where_clause}
        RETURN i.id AS id, i.summary AS summary, u.displayName AS assignee,
               labels(i) AS labels, i.updated_at AS updated_at
        ORDER BY i.updated_at DESC
        LIMIT $limit
        """

        return query.strip(), params

    @staticmethod
    def build_project_search_query(project_name: str, date_filter: Optional[datetime], limit: int) -> Tuple[str, Dict[str, Any]]:
        """Build secure query for project search"""
        params = {
            'project_name': project_name,
            'project_id': project_name.upper(),
            'limit': limit
        }

        where_clauses = ["(p.name CONTAINS $project_name OR p.id = $project_id)"]

        if date_filter:
            params['target_date'] = date_filter.date().isoformat()
            where_clauses.append("date(i.updated_at) = date($target_date)")

        where_clause = " AND ".join(where_clauses)

        query = f"""
        MATCH (p:JiraProject)-[:HAS_ISSUE]->(i:JiraIssue)
        WHERE {where_clause}
        RETURN i.id AS id, i.summary AS summary, p.name AS project,
               labels(i) AS labels, i.updated_at AS updated_at
        ORDER BY i.updated_at DESC
        LIMIT $limit
        """

        return query.strip(), params

    @staticmethod
    def build_relationship_query(issue_id: str, relationship_type: str, date_filter: Optional[datetime], limit: int) -> Tuple[str, Dict[str, Any]]:
        """Build secure query for relationship search"""
        params = {
            'issue_id': issue_id.upper(),
            'limit': limit
        }

        # Map relationship types to Cypher relationship names
        relationship_mapping = {
            'blocking': 'BLOCKS',
            'blocked by': 'BLOCKS',
            'depends on': 'DEPENDS_ON',
            'relates to': 'RELATES_TO'
        }

        cypher_relationship = relationship_mapping.get(relationship_type.lower(), 'RELATES_TO')

        where_clauses = ["i1.id = $issue_id"]

        if date_filter:
            params['target_date'] = date_filter.date().isoformat()
            where_clauses.append("date(i2.updated_at) = date($target_date)")

        where_clause = " AND ".join(where_clauses)

        query = f"""
        MATCH (i1:JiraIssue)-[:{cypher_relationship}]->(i2:JiraIssue)
        WHERE {where_clause}
        RETURN i2.id AS related_issue_id, i2.summary AS related_issue_summary,
               '{cypher_relationship}' AS relationship_type, i2.updated_at AS updated_at
        ORDER BY i2.updated_at DESC
        LIMIT $limit
        """

        return query.strip(), params


class SearchCache:
    """Simple in-memory cache for search results"""

    def __init__(self, ttl_seconds: int = 300):
        self.cache = {}
        self.ttl_seconds = ttl_seconds

    def _generate_cache_key(self, query: str, params: Dict[str, Any]) -> str:
        """Generate cache key from query and parameters"""
        cache_data = {'query': query, 'params': sorted(params.items())}
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def get(self, query: str, params: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Get cached results if available and not expired"""
        cache_key = self._generate_cache_key(query, params)

        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if datetime.now().timestamp() - timestamp < self.ttl_seconds:
                logger.debug(f"Cache hit for query: {query[:50]}...")
                return cached_data
            else:
                # Remove expired entry
                del self.cache[cache_key]

        return None

    def set(self, query: str, params: Dict[str, Any], results: List[Dict[str, Any]]) -> None:
        """Cache search results"""
        cache_key = self._generate_cache_key(query, params)
        self.cache[cache_key] = (results, datetime.now().timestamp())
        logger.debug(f"Cached results for query: {query[:50]}...")

    def clear(self) -> None:
        """Clear all cached results"""
        self.cache.clear()
        logger.info("Search cache cleared")

class JiraConnectorService(BaseConnector):
    """
    Enhanced Service implementation for Jira Connector.
    This class provides a robust, secure, and efficient search implementation using Cypher
    for a structured data source like Jira with advanced query processing and caching.
    """
    CONNECTOR_TYPE = "structured"

    def __init__(self, config: Dict[str, Any]):
        # Store configuration
        self.config = config
        # Initialize Jira API client or connection details
        self.jira_client = None

        # Initialize search configuration
        self.search_config = SearchConfig(
            max_results=config.get('max_results', 50),
            default_limit=config.get('default_limit', 10),
            cache_ttl_seconds=config.get('cache_ttl_seconds', 300),
            enable_fuzzy_search=config.get('enable_fuzzy_search', True),
            enable_caching=config.get('enable_caching', True),
            query_timeout_seconds=config.get('query_timeout_seconds', 30),
            max_query_length=config.get('max_query_length', 1000),
            enable_query_logging=config.get('enable_query_logging', True)
        )

        # Initialize cache if enabled
        self.search_cache = SearchCache(self.search_config.cache_ttl_seconds) if self.search_config.enable_caching else None

        # Initialize query parser and builder
        self.query_parser = AdvancedQueryParser()
        self.cypher_builder = SecureCypherBuilder()

        logger.info(f"JiraConnectorService initialized with config: {self.config}")
        logger.info(f"Search configuration: {self.search_config}")

    def connect(self) -> Any:
        """Establish connection to Jira"""
        # Placeholder for actual Jira connection logic
        logger.info("Connecting to Jira...")
        return {"status": "connected", "jira_url": self.config.get("jira_url")}

    def get_connector(self) -> dict:
        """Returns metadata about the Jira connector"""
        return {
            "name": "Jira Connector",
            "type": self.CONNECTOR_TYPE,
            "version": "2.0.0",
            "description": "Enhanced Jira connector with advanced search capabilities",
            "capabilities": [
                "natural_language_search",
                "query_caching",
                "security_validation",
                "performance_monitoring"
            ],
            "supported_entities": list(get_all_entity_types()),
            "supported_relationships": list(get_all_relationship_types())
        }

    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """Fetch all Jira data with pagination"""
        # Placeholder implementation
        logger.info("Fetching Jira data...")
        sample_data = [
            {"id": "ISSUE-1", "type": "JiraIssue", "summary": "Sample issue 1"},
            {"id": "ISSUE-2", "type": "JiraIssue", "summary": "Sample issue 2"},
            {"id": "USER-1", "type": "JiraUser", "displayName": "Alice Johnson"},
            {"id": "PROJ-1", "type": "JiraProject", "name": "Project Alpha"}
        ]
        for item in sample_data:
            yield item

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """Fetch a single Jira entity by ID"""
        logger.info(f"Fetching Jira entity: {id}")
        # Placeholder implementation
        return {
            "id": id,
            "type": "JiraIssue",
            "summary": f"Sample issue {id}",
            "description": f"Description for {id}",
            "status": "Open"
        }

    def sync(self):
        """Perform full sync of Jira data"""
        logger.info("Starting full Jira sync...")
        # Placeholder implementation
        return {"status": "completed", "synced_items": 100}

    def sync_by_id(self, id: str):
        """Perform partial sync for a single Jira entity"""
        logger.info(f"Syncing Jira entity: {id}")
        # Placeholder implementation
        return {"status": "completed", "synced_item": id}

    def store_context(self, data: Any):
        """Store context and embedding for Jira data"""
        logger.info("Storing Jira context and embeddings...")
        # Placeholder implementation
        return {"status": "stored", "data_id": getattr(data, 'id', 'unknown')}


    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        Enhanced search method with advanced query processing, security, and caching.

        Args:
            query: Natural language search query

        Returns:
            List of search results

        Raises:
            ValueError: If query is invalid or contains dangerous patterns
            Exception: For other search-related errors
        """
        try:
            start_time = datetime.now()

            if self.search_config.enable_query_logging:
                logger.info(f"Received search query: '{query}'")

            # Parse and validate query
            parsed_query = self.query_parser.parse_query(query, self.search_config)

            if self.search_config.enable_query_logging:
                logger.debug(f"Parsed query: {parsed_query}")

            # Check cache first if enabled
            if self.search_cache:
                cached_results = self._check_cache(parsed_query)
                if cached_results is not None:
                    return cached_results

            # Build secure Cypher query
            cypher_query, params = self._build_cypher_query(parsed_query)

            if self.search_config.enable_query_logging:
                logger.debug(f"Generated Cypher query: {cypher_query}")
                logger.debug(f"Query parameters: {params}")

            # Execute query (placeholder implementation)
            results = self._execute_query(cypher_query, params, parsed_query)

            # Cache results if enabled
            if self.search_cache and results:
                self.search_cache.set(cypher_query, params, results)

            # Log performance metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"JIRA Search completed in {execution_time:.3f}s, returned {len(results)} results")

            return results

        except ValueError as e:
            logger.warning(f"Invalid search query: {e}")
            raise
        except Exception as e:
            logger.error(f"Search error: {e}")
            raise Exception(f"Search failed: {str(e)}")

    def _check_cache(self, parsed_query: ParsedQuery) -> Optional[List[Dict[str, Any]]]:
        """Check cache for existing results"""
        # Create a simple cache key from parsed query
        cache_key = f"{parsed_query.query_type.value}_{hash(str(parsed_query))}"
        return self.search_cache.get(cache_key, {})

    def _build_cypher_query(self, parsed_query: ParsedQuery) -> Tuple[str, Dict[str, Any]]:
        """Build secure Cypher query based on parsed query"""
        if parsed_query.query_type == QueryType.ISSUE_SEARCH:
            return self.cypher_builder.build_issue_search_query(
                parsed_query.keywords,
                parsed_query.date_filter,
                parsed_query.limit
            )
        elif parsed_query.query_type == QueryType.USER_ASSIGNMENT:
            user_name = parsed_query.entity_filters.get('user', '')
            return self.cypher_builder.build_user_assignment_query(
                user_name,
                parsed_query.date_filter,
                parsed_query.limit
            )
        elif parsed_query.query_type == QueryType.PROJECT_SEARCH:
            project_name = parsed_query.entity_filters.get('project', '')
            return self.cypher_builder.build_project_search_query(
                project_name,
                parsed_query.date_filter,
                parsed_query.limit
            )
        elif parsed_query.query_type == QueryType.RELATIONSHIP_SEARCH:
            issue_id = parsed_query.entity_filters.get('issue_id', '')
            return self.cypher_builder.build_relationship_query(
                issue_id,
                parsed_query.relationship_type or 'relates to',
                parsed_query.date_filter,
                parsed_query.limit
            )
        elif parsed_query.query_type == QueryType.ENTITY_LISTING:
            return self._build_entity_listing_query(parsed_query)
        else:
            # Generic search fallback
            return self.cypher_builder.build_issue_search_query(
                parsed_query.keywords,
                parsed_query.date_filter,
                parsed_query.limit
            )

    def _build_entity_listing_query(self, parsed_query: ParsedQuery) -> Tuple[str, Dict[str, Any]]:
        """Build query for entity listing"""
        entity_type = parsed_query.entity_filters.get('entity_type', '').lower()
        params = {'limit': parsed_query.limit}

        entity_mapping = {
            'users': 'JiraUser',
            'user': 'JiraUser',
            'projects': 'JiraProject',
            'project': 'JiraProject',
            'components': 'JiraComponent',
            'component': 'JiraComponent',
            'sprints': 'JiraSprint',
            'sprint': 'JiraSprint',
            'versions': 'JiraVersion',
            'version': 'JiraVersion'
        }

        cypher_entity = entity_mapping.get(entity_type, 'JiraIssue')
        entity_var = cypher_entity[4:].lower()  # Remove 'Jira' prefix

        query = f"""
        MATCH ({entity_var}:{cypher_entity})
        RETURN {entity_var}.id AS id,
               {entity_var}.name AS name,
               {entity_var}.displayName AS displayName,
               labels({entity_var}) AS labels
        ORDER BY {entity_var}.name
        LIMIT $limit
        """

        return query.strip(), params

    def _execute_query(self, cypher_query: str, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Execute Cypher query (placeholder implementation with enhanced simulation)"""
        logger.debug(f"Executing query: {cypher_query}")

        # Enhanced simulation based on query type and parameters
        if parsed_query.query_type == QueryType.ISSUE_SEARCH:
            return self._simulate_issue_search_results(params, parsed_query)
        elif parsed_query.query_type == QueryType.USER_ASSIGNMENT:
            return self._simulate_user_assignment_results(params, parsed_query)
        elif parsed_query.query_type == QueryType.PROJECT_SEARCH:
            return self._simulate_project_search_results(params, parsed_query)
        elif parsed_query.query_type == QueryType.RELATIONSHIP_SEARCH:
            return self._simulate_relationship_search_results(params, parsed_query)
        elif parsed_query.query_type == QueryType.ENTITY_LISTING:
            return self._simulate_entity_listing_results(params, parsed_query)
        else:
            return self._simulate_generic_search_results(params, parsed_query)

    def _simulate_issue_search_results(self, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Simulate issue search results"""
        base_results = [
            {
                "id": "ISSUE-1",
                "summary": "Bug fix in login flow",
                "description": "Critical bug affecting user authentication",
                "labels": ["JiraIssue", "Bug"],
                "updated_at": (datetime.now() - timedelta(days=1)).isoformat()
            },
            {
                "id": "ISSUE-2",
                "summary": "Feature: Enhanced user profile",
                "description": "Add new fields to user profile page",
                "labels": ["JiraIssue", "Feature"],
                "updated_at": datetime.now().isoformat()
            },
            {
                "id": "ISSUE-3",
                "summary": "Performance optimization for search",
                "description": "Improve search response times",
                "labels": ["JiraIssue", "Performance"],
                "updated_at": (datetime.now() - timedelta(hours=6)).isoformat()
            }
        ]

        # Filter by keywords if present
        if 'keyword_0' in params:
            keyword = params['keyword_0'].lower()
            base_results = [
                result for result in base_results
                if keyword in result['summary'].lower() or keyword in result['description'].lower()
            ]

        return self._apply_date_filter(base_results, params)

    def _simulate_user_assignment_results(self, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Simulate user assignment search results"""
        user_name = params.get('user_name', '').lower()

        all_results = [
            {
                "id": "ISSUE-1",
                "summary": "Bug fix in login",
                "assignee": "Alice Johnson",
                "labels": ["JiraIssue"],
                "updated_at": (datetime.now() - timedelta(days=1)).isoformat()
            },
            {
                "id": "ISSUE-4",
                "summary": "Database migration",
                "assignee": "Bob Smith",
                "labels": ["JiraIssue"],
                "updated_at": datetime.now().isoformat()
            },
            {
                "id": "ISSUE-5",
                "summary": "UI improvements",
                "assignee": "Alice Johnson",
                "labels": ["JiraIssue"],
                "updated_at": (datetime.now() - timedelta(hours=3)).isoformat()
            }
        ]

        # Filter by user name
        filtered_results = [
            result for result in all_results
            if user_name in result['assignee'].lower()
        ]

        return self._apply_date_filter(filtered_results, params)

    def _simulate_project_search_results(self, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Simulate project search results"""
        project_name = params.get('project_name', '').lower()

        all_results = [
            {
                "id": "ISSUE-1",
                "summary": "Bug fix in login",
                "project": "Project Alpha",
                "labels": ["JiraIssue"],
                "updated_at": (datetime.now() - timedelta(days=1)).isoformat()
            },
            {
                "id": "ISSUE-6",
                "summary": "New feature development",
                "project": "Project Beta",
                "labels": ["JiraIssue"],
                "updated_at": datetime.now().isoformat()
            }
        ]

        # Filter by project name
        filtered_results = [
            result for result in all_results
            if project_name in result['project'].lower()
        ]

        return self._apply_date_filter(filtered_results, params)

    def _simulate_relationship_search_results(self, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Simulate relationship search results"""
        return [
            {
                "related_issue_id": "ISSUE-7",
                "related_issue_summary": "Dependency for main feature",
                "relationship_type": "BLOCKS",
                "updated_at": (datetime.now() - timedelta(days=2)).isoformat()
            }
        ]

    def _simulate_entity_listing_results(self, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Simulate entity listing results"""
        entity_type = parsed_query.entity_filters.get('entity_type', '').lower()

        if 'user' in entity_type:
            return [
                {"id": "USER1", "name": "Alice Johnson", "displayName": "Alice Johnson", "labels": ["JiraUser"]},
                {"id": "USER2", "name": "Bob Smith", "displayName": "Bob Smith", "labels": ["JiraUser"]}
            ]
        elif 'project' in entity_type:
            return [
                {"id": "PROJ1", "name": "Project Alpha", "displayName": "Project Alpha", "labels": ["JiraProject"]},
                {"id": "PROJ2", "name": "Project Beta", "displayName": "Project Beta", "labels": ["JiraProject"]}
            ]
        else:
            return [
                {"id": "COMP1", "name": "Frontend", "displayName": "Frontend", "labels": ["JiraComponent"]},
                {"id": "COMP2", "name": "Backend", "displayName": "Backend", "labels": ["JiraComponent"]}
            ]

    def _simulate_generic_search_results(self, params: Dict[str, Any], parsed_query: ParsedQuery) -> List[Dict[str, Any]]:
        """Simulate generic search results"""
        return [
            {
                "id": "GENERIC-1",
                "name": "Generic search result",
                "summary": "A generic item matching your search",
                "labels": ["GenericEntity"],
                "updated_at": datetime.now().isoformat()
            }
        ]

    def _apply_date_filter(self, results: List[Dict[str, Any]], params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply date filter to results if specified"""
        if 'target_date' not in params:
            return results

        target_date_str = params['target_date']
        target_date = datetime.fromisoformat(target_date_str).date()

        filtered_results = []
        for result in results:
            if 'updated_at' in result:
                try:
                    result_date = datetime.fromisoformat(result['updated_at']).date()
                    if result_date == target_date:
                        filtered_results.append(result)
                except ValueError:
                    # Skip results with invalid date format
                    continue

        return filtered_results

    def clear_cache(self) -> None:
        """Clear search cache"""
        if self.search_cache:
            self.search_cache.clear()
            logger.info("Search cache cleared")

    def get_search_stats(self) -> Dict[str, Any]:
        """Get search performance statistics"""
        return {
            "cache_enabled": self.search_config.enable_caching,
            "cache_size": len(self.search_cache.cache) if self.search_cache else 0,
            "config": {
                "max_results": self.search_config.max_results,
                "default_limit": self.search_config.default_limit,
                "cache_ttl_seconds": self.search_config.cache_ttl_seconds,
                "enable_fuzzy_search": self.search_config.enable_fuzzy_search,
                "query_timeout_seconds": self.search_config.query_timeout_seconds
            }
        }


# Example usage (for testing purposes, not part of the service itself)
if __name__ == "__main__":
    # This part would typically be handled by the ConnectorFactory and application logic
    # For demonstration, we'll instantiate and test the enhanced search functionality.

    # Configure logging for demo
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Enhanced mock config with search settings
    mock_config = {
        "jira_url": "https://your-jira-instance.atlassian.net",
        "username": "api_user",
        "password": "api_password",
        "max_results": 50,
        "default_limit": 10,
        "cache_ttl_seconds": 300,
        "enable_fuzzy_search": True,
        "enable_caching": True,
        "query_timeout_seconds": 30,
        "enable_query_logging": True
    }

    jira_service = JiraConnectorService(mock_config)
    jira_service.connect()  # Simulate connection

    print("\n" + "="*60)
    print("ENHANCED JIRA SEARCH TESTING")
    print("="*60)

    # Test various query types
    test_queries = [
        "find issues with bug",
        "issues assigned to Alice",
        "show issues in project Alpha",
        "what issues are blocking ISSUE-1",
        "find all users",
        "show all projects",
        "search for performance optimization",
        "what did Alice do yesterday",
        "issues updated today",
        "issues updated on 2025-06-29",
        "limit 5 issues with login",
        "components",
        "sprints in project Alpha"
    ]

    for query in test_queries:
        print(f"\n--- Query: '{query}' ---")
        try:
            results = jira_service.search(query)
            print(f"Results: {len(results)} items found")
            for result in results[:2]:  # Show first 2 results
                print(f"  - {result}")
        except Exception as e:
            print(f"Error: {e}")

    # Test search statistics
    print(f"\n--- Search Statistics ---")
    stats = jira_service.get_search_stats()
    print(f"Stats: {stats}")

    # Test cache functionality
    print(f"\n--- Testing Cache ---")
    print("Running same query twice to test caching...")
    query = "find issues with bug"

    # First run
    start_time = datetime.now()
    results1 = jira_service.search(query)
    time1 = (datetime.now() - start_time).total_seconds()

    # Second run (should be cached)
    start_time = datetime.now()
    results2 = jira_service.search(query)
    time2 = (datetime.now() - start_time).total_seconds()

    print(f"First run: {time1:.3f}s, Second run: {time2:.3f}s")
    print(f"Results match: {results1 == results2}")

    # Test security features
    print(f"\n--- Testing Security Features ---")
    dangerous_queries = [
        "'; DROP TABLE issues; --",
        "<script>alert('xss')</script>",
        "issues with summary containing 'test'",  # This should work
    ]

    for dangerous_query in dangerous_queries:
        print(f"Testing: '{dangerous_query}'")
        try:
            results = jira_service.search(dangerous_query)
            print(f"  ✓ Safe query processed, {len(results)} results")
        except ValueError as e:
            print(f"  ✗ Blocked dangerous query: {e}")
        except Exception as e:
            print(f"  ⚠ Unexpected error: {e}")

    print(f"\n--- Testing Complete ---")
