import re
from datetime import datetime, timedelta
from modules.connectors.base import BaseConnector
from typing import Any, Dict, List, Iterator
from modules.connectors.handlers.Jira.constants.entities import EntityType
from modules.connectors.handlers.Jira.constants.relationships import RelationshipType

class JiraConnectorService(BaseConnector):
    """
    Service implementation for Jira Connector.
    This class demonstrates a robust search implementation using Cypher
    for a structured data source like Jira.
    """
    CONNECTOR_TYPE = "structured"

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # Placeholder for Jira API client or connection details
        self.jira_client = None 
        print(f"JiraConnectorService initialized with config: {self.config}")

    def connect(self) -> Any:
        """
        Establishes and tests the connection to the Jira instance.
        In a real scenario, this would initialize the Jira API client.
        """
        print("Connecting to <PERSON><PERSON> (placeholder)...")
        # Example: self.jira_client = Jira(options={'server': self.config['jira_url']}, basic_auth=(self.config['username'], self.config['password']))
        self.jira_client = True # Simulate successful connection
        return self.jira_client

    def get_connector(self) -> dict:
        """
        Returns metadata about the Jira connector.
        """
        return {
            "source_type": "jira",
            "name": "Jira Connector",
            "connector_type": self.CONNECTOR_TYPE,
            "category": "Task Management",
            "icon": "base64_encoded_jira_icon", # Replace with actual icon
            "description": "Connects to Jira to sync issues, projects, and users.",
            "purpose": "To integrate Jira data into the knowledge graph for enhanced search and analysis.",
            "nodes": list(EntityType.get_all_entity_types()),
            "relationships": list(RelationshipType.get_all_relationship_types()),
            "example_usage": "Fetches Jira projects and their associated issues.",
            "example_queries": [
                "MATCH (p:JiraProject)-[:HAS_ISSUE]->(i:JiraIssue) RETURN p.name, i.summary",
                "MATCH (u:JiraUser)-[:ASSIGNED_TO]->(i:JiraIssue) WHERE u.displayName = 'John Doe' RETURN i.summary"
            ]
        }

    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """
        Pulls all data from Jira with pagination.
        This is a placeholder. In a real implementation, it would fetch
        projects, issues, users, etc., from Jira API.
        """
        print("Fetching all data from Jira (placeholder)...")
        # Yield sample data for demonstration
        yield {"type": "JiraProject", "id": "PROJ1", "name": "Project Alpha"}
        yield {"type": "JiraIssue", "id": "ISSUE-1", "summary": "Bug fix", "project_id": "PROJ1"}
        yield {"type": "JiraUser", "id": "USER1", "displayName": "Alice"}

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """
        Fetch a single entity from Jira by its ID.
        Placeholder implementation.
        """
        print(f"Fetching data for ID: {id} from Jira (placeholder)...")
        if id == "ISSUE-1":
            return {"type": "JiraIssue", "id": "ISSUE-1", "summary": "Bug fix", "description": "Fix critical bug in login flow."}
        return {}

    def sync(self):
        """
        Perform a full sync of the Jira data source.
        This would typically involve calling fetch_data and then
        processing and storing the data.
        """
        print("Performing full sync for Jira (placeholder)...")
        for data_item in self.fetch_data():
            print(f"Syncing: {data_item}")
            # In a real scenario, this would involve transforming and storing data
            # self.store_context(data_item)

    def sync_by_id(self, id: str):
        """
        Perform a partial sync for a single Jira entity.
        """
        print(f"Performing partial sync for Jira ID: {id} (placeholder)...")
        data_item = self.fetch_data_by_id(id)
        if data_item:
            print(f"Syncing: {data_item}")
            # self.store_context(data_item)

    def store_context(self, data: Any):
        """
        Stores both context and embedding for given Jira data.
        Placeholder implementation.
        """
        print(f"Storing context for Jira data (placeholder): {data}")
        # This would involve:
        # 1. Extracting relevant text for embedding.
        # 2. Generating embedding.
        # 3. Storing raw data and embedding in appropriate storage.

    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        Performs a robust search within the Jira knowledge graph using Cypher.
        This implementation attempts to interpret various natural language queries
        and translate them into appropriate Cypher queries.
        """
        print(f"Received search query for Jira: '{query}'")
        results = []
        cypher_query = ""

import re
from datetime import datetime, timedelta
from modules.connectors.base import BaseConnector
from typing import Any, Dict, List, Iterator
from modules.connectors.handlers.Jira.constants.entities import EntityType
from modules.connectors.handlers.Jira.constants.relationships import RelationshipType

class JiraConnectorService(BaseConnector):
    """
    Service implementation for Jira Connector.
    This class demonstrates a robust search implementation using Cypher
    for a structured data source like Jira.
    """
    CONNECTOR_TYPE = "structured"

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # Placeholder for Jira API client or connection details
        self.jira_client = None
        print(f"JiraConnectorService initialized with config: {self.config}")

    def connect(self) -> Any:
        """
        Establishes and tests the connection to the Jira instance.
        In a real scenario, this would initialize the Jira API client.
        """
        print("Connecting to Jira (placeholder)...")
        # Example: self.jira_client = Jira(options={'server': self.config['jira_url']}, basic_auth=(self.config['username'], self.config['password']))
        self.jira_client = True # Simulate successful connection
        return self.jira_client

    def get_connector(self) -> dict:
        """
        Returns metadata about the Jira connector.
        """
        return {
            "source_type": "jira",
            "name": "Jira Connector",
            "connector_type": self.CONNECTOR_TYPE,
            "category": "Task Management",
            "icon": "base64_encoded_jira_icon", # Replace with actual icon
            "description": "Connects to Jira to sync issues, projects, and users.",
            "purpose": "To integrate Jira data into the knowledge graph for enhanced search and analysis.",
            "nodes": list(EntityType.get_all_entity_types()),
            "relationships": list(RelationshipType.get_all_relationship_types()),
            "example_usage": "Fetches Jira projects and their associated issues.",
            "example_queries": [
                "MATCH (p:JiraProject)-[:HAS_ISSUE]->(i:JiraIssue) RETURN p.name, i.summary",
                "MATCH (u:JiraUser)-[:ASSIGNED_TO]->(i:JiraIssue) WHERE u.displayName = 'John Doe' RETURN i.summary"
            ]
        }

    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """
        Pulls all data from Jira with pagination.
        This is a placeholder. In a real implementation, it would fetch
        projects, issues, users, etc., from Jira API.
        """
        print("Fetching all data from Jira (placeholder)...")
        # Yield sample data for demonstration
        yield {"type": "JiraProject", "id": "PROJ1", "name": "Project Alpha"}
        yield {"type": "JiraIssue", "id": "ISSUE-1", "summary": "Bug fix", "project_id": "PROJ1", "updated_at": (datetime.now() - timedelta(days=1)).isoformat()}
        yield {"type": "JiraUser", "id": "USER1", "displayName": "Alice"}
        yield {"type": "JiraIssue", "id": "ISSUE-2", "summary": "Feature development", "project_id": "PROJ1", "updated_at": datetime.now().isoformat()}

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """
        Fetch a single entity from Jira by its ID.
        Placeholder implementation.
        """
        print(f"Fetching data for ID: {id} from Jira (placeholder)...")
        if id == "ISSUE-1":
            return {"type": "JiraIssue", "id": "ISSUE-1", "summary": "Bug fix", "description": "Fix critical bug in login flow.", "updated_at": (datetime.now() - timedelta(days=1)).isoformat()}
        return {}

    def sync(self):
        """
        Perform a full sync of the Jira data source.
        This would typically involve calling fetch_data and then
        processing and storing the data.
        """
        print("Performing full sync for Jira (placeholder)...")
        for data_item in self.fetch_data():
            print(f"Syncing: {data_item}")
            # In a real scenario, this would involve transforming and storing data
            # self.store_context(data_item)

    def sync_by_id(self, id: str):
        """
        Perform a partial sync for a single Jira entity.
        """
        print(f"Performing partial sync for Jira ID: {id} (placeholder)...")
        data_item = self.fetch_data_by_id(id)
        if data_item:
            print(f"Syncing: {data_item}")
            # self.store_context(data_item)

    def store_context(self, data: Any):
        """
        Stores both context and embedding for given Jira data.
        Placeholder implementation.
        """
        print(f"Storing context for Jira data (placeholder): {data}")
        # This would involve:
        # 1. Extracting relevant text for embedding.
        # 2. Generating embedding.
        # 3. Storing raw data and embedding in appropriate storage.

    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        Performs a robust search within the Jira knowledge graph using Cypher.
        This implementation attempts to interpret various natural language queries
        and translate them into appropriate Cypher queries.
        """
        print(f"Received search query for Jira: '{query}'")
        results = []
        cypher_query = ""
        date_filter = ""

        query_lower = query.lower()

        # --- Date Parsing ---
        target_date = None
        if "yesterday" in query_lower:
            target_date = datetime.now() - timedelta(days=1)
            date_filter = f"AND date(i.updated_at) = date('{target_date.isoformat()}')"
            print(f"Detected 'yesterday'. Filtering for date: {target_date.date()}")
        elif "today" in query_lower:
            target_date = datetime.now()
            date_filter = f"AND date(i.updated_at) = date('{target_date.isoformat()}')"
            print(f"Detected 'today'. Filtering for date: {target_date.date()}")
        elif re.search(r"\bon\s+(\d{4}-\d{2}-\d{2})\b", query_lower):
            match = re.search(r"\bon\s+(\d{4}-\d{2}-\d{2})\b", query_lower)
            target_date_str = match.group(1)
            try:
                target_date = datetime.strptime(target_date_str, "%Y-%m-%d")
                date_filter = f"AND date(i.updated_at) = date('{target_date.isoformat()}')"
                print(f"Detected specific date '{target_date_str}'. Filtering for date: {target_date.date()}")
            except ValueError:
                print(f"Could not parse date: {target_date_str}")

        # --- Query Interpretation and Cypher Generation ---

        # 1. Search for issues by summary/description keywords
        if "issue" in query_lower or "ticket" in query_lower:
            keywords = [word for word in query_lower.split() if word not in ["issue", "ticket", "find", "search", "for", "about", "with", "summary", "description", "yesterday", "today", "on"]]
            if keywords:
                keyword_pattern = "|".join(keywords)
                cypher_query = f"""
                MATCH (i:JiraIssue)
                WHERE (i.summary CONTAINS '{keywords[0]}' OR i.description CONTAINS '{keywords[0]}') {date_filter}
                RETURN i.id AS id, i.summary AS summary, i.description AS description, labels(i) AS labels, i.updated_at AS updated_at
                LIMIT 10
                """
                print(f"Generated Cypher for issue keyword search: {cypher_query}")
            else:
                cypher_query = f"MATCH (i:JiraIssue) WHERE true {date_filter} RETURN i.id AS id, i.summary AS summary, labels(i) AS labels, i.updated_at AS updated_at LIMIT 10"
                print(f"Generated Cypher for all issues with date filter: {cypher_query}")

        # 2. Search for issues assigned to a specific user (with optional date filter)
        elif "assigned to" in query_lower or "by user" in query_lower:
            user_name_match = re.search(r"(assigned to|by user)\s+([\"']?)(.*?)([\"']?)(?:\s|$)", query_lower)
            if user_name_match:
                user_name = user_name_match.group(3).strip()
                cypher_query = f"""
                MATCH (u:JiraUser)-[:ASSIGNED_TO]->(i:JiraIssue)
                WHERE u.displayName CONTAINS '{user_name}' {date_filter}
                RETURN i.id AS id, i.summary AS summary, u.displayName AS assignee, labels(i) AS labels, i.updated_at AS updated_at
                LIMIT 10
                """
                print(f"Generated Cypher for issues assigned to user with date filter: {cypher_query}")

        # 3. Search for issues within a specific project (with optional date filter)
        elif "in project" in query_lower or "project" in query_lower:
            project_name_match = re.search(r"(in project|project)\s+([\"']?)(.*?)([\"']?)(?:\s|$)", query_lower)
            if project_name_match:
                project_name = project_name_match.group(3).strip()
                cypher_query = f"""
                MATCH (p:JiraProject)-[:HAS_ISSUE]->(i:JiraIssue)
                WHERE (p.name CONTAINS '{project_name}' OR p.id = '{project_name.upper()}') {date_filter}
                RETURN i.id AS id, i.summary AS summary, p.name AS project, labels(i) AS labels, i.updated_at AS updated_at
                LIMIT 10
                """
                print(f"Generated Cypher for issues in project with date filter: {cypher_query}")
            else:
                cypher_query = "MATCH (p:JiraProject) RETURN p.id AS id, p.name AS name, labels(p) AS labels LIMIT 10"
                print(f"Generated Cypher for all projects: {cypher_query}")

        # 4. Search for relationships (e.g., "issues blocking", "issues related to")
        elif "blocking" in query_lower or "depends on" in query_lower or "relates to" in query_lower:
            if "blocking" in query_lower:
                relation_type = "BLOCKS"
            elif "depends on" in query_lower:
                relation_type = "DEPENDS_ON"
            elif "relates to" in query_lower:
                relation_type = "RELATES_TO"
            else:
                relation_type = None

            if relation_type:
                issue_id_match = re.search(r"issue\s+([A-Z]+-\d+)", query_lower)
                if issue_id_match:
                    issue_id = issue_id_match.group(1).upper()
                    cypher_query = f"""
                    MATCH (i1:JiraIssue)-[:{relation_type}]->(i2:JiraIssue)
                    WHERE i1.id = '{issue_id}' {date_filter.replace('i.updated_at', 'i2.updated_at')}
                    RETURN i2.id AS related_issue_id, i2.summary AS related_issue_summary, type(r) AS relationship_type, i2.updated_at AS updated_at
                    LIMIT 10
                    """
                    print(f"Generated Cypher for relationship search with date filter: {cypher_query}")
                else:
                    cypher_query = f"MATCH (i1:JiraIssue)-[r:{relation_type}]->(i2:JiraIssue) WHERE true {date_filter.replace('i.updated_at', 'i2.updated_at')} RETURN i1.id, i2.id, type(r) AS relationship_type, i2.updated_at AS updated_at LIMIT 10"
                    print(f"Generated Cypher for all {relation_type} relationships with date filter: {cypher_query}")

        # 5. General entity search (e.g., "find users", "show components")
        elif "users" in query_lower:
            cypher_query = "MATCH (u:JiraUser) RETURN u.id AS id, u.displayName AS name, labels(u) AS labels LIMIT 10"
            print(f"Generated Cypher for all users: {cypher_query}")
        elif "components" in query_lower:
            cypher_query = "MATCH (c:JiraComponent) RETURN c.id AS id, c.name AS name, labels(c) AS labels LIMIT 10"
            print(f"Generated Cypher for all components: {cypher_query}")
        elif "sprints" in query_lower:
            cypher_query = "MATCH (s:JiraSprint) RETURN s.id AS id, s.name AS name, labels(s) AS labels LIMIT 10"
            print(f"Generated Cypher for all sprints: {cypher_query}")
        elif "versions" in query_lower:
            cypher_query = "MATCH (v:JiraVersion) RETURN v.id AS id, v.name AS name, labels(v) AS labels LIMIT 10"
            print(f"Generated Cypher for all versions: {cypher_query}")

        # 6. Fallback: Generic keyword search across all Jira entities (with optional date filter)
        if not cypher_query:
            keywords = query.split()
            if keywords:
                keyword_pattern = "|".join(keywords)
                cypher_query = f"""
                MATCH (n)
                WHERE (n.summary IS NOT NULL AND n.summary CONTAINS '{keywords[0]}')
                   OR (n.description IS NOT NULL AND n.description CONTAINS '{keywords[0]}')
                   OR (n.name IS NOT NULL AND n.name CONTAINS '{keywords[0]}') {date_filter.replace('i.updated_at', 'n.updated_at')}
                RETURN n.id AS id, n.name AS name, n.summary AS summary, labels(n) AS labels, n.updated_at AS updated_at
                LIMIT 10
                """
                print(f"Generated Cypher for generic keyword search with date filter: {cypher_query}")
            else:
                print("No specific query pattern matched. Returning empty results.")
                return []

        # --- Execute Cypher Query (Placeholder) ---
        print(f"Executing Cypher query (placeholder): {cypher_query}")
        # In a real scenario, this would execute the Cypher query against a Neo4j/graph database
        # connected to Jira data.
        
        # Simulate results based on query type for demonstration
        if "JiraIssue" in cypher_query and "PROJ1" in cypher_query:
            results = [
                {"id": "ISSUE-1", "summary": "Bug fix in login", "project": "Project Alpha", "labels": ["JiraIssue"], "updated_at": (datetime.now() - timedelta(days=1)).isoformat()},
                {"id": "ISSUE-2", "summary": "Feature: User profile", "project": "Project Alpha", "labels": ["JiraIssue"], "updated_at": datetime.now().isoformat()}
            ]
        elif "JiraUser" in cypher_query and "Alice" in cypher_query:
             results = [
                {"id": "USER1", "name": "Alice", "labels": ["JiraUser"]}
            ]
        elif "JiraProject" in cypher_query:
            results = [
                {"id": "PROJ1", "name": "Project Alpha", "labels": ["JiraProject"]},
                {"id": "PROJ2", "name": "Project Beta", "labels": ["JiraProject"]}
            ]
        elif "BLOCKS" in cypher_query:
            results = [
                {"related_issue_id": "ISSUE-3", "related_issue_summary": "Blocked by ISSUE-1", "relationship_type": "BLOCKS", "updated_at": (datetime.now() - timedelta(days=2)).isoformat()}
            ]
        else:
            results = [
                {"id": "GENERIC-1", "name": "Generic Item", "summary": "A generic search result", "labels": ["GenericEntity"], "updated_at": datetime.now().isoformat()}
            ]

        # Filter simulated results by date if a filter was applied
        if target_date:
            filtered_results = []
            for item in results:
                if "updated_at" in item:
                    item_date = datetime.fromisoformat(item["updated_at"]).date()
                    if item_date == target_date.date():
                        filtered_results.append(item)
            results = filtered_results
            print(f"Simulated results after date filter: {results}")


        print(f"Simulated search results: {results}")
        return results

# Example usage (for testing purposes, not part of the service itself)
if __name__ == "__main__":
    # This part would typically be handled by the ConnectorFactory and application logic
    # For demonstration, we'll instantiate and test search directly.
    
    # Mock config
    mock_config = {
        "jira_url": "https://your-jira-instance.atlassian.net",
        "username": "api_user",
        "password": "api_password"
    }
    
    jira_service = JiraConnectorService(mock_config)
    jira_service.connect() # Simulate connection

    print("\n--- Testing Jira Search ---")

    print("\nQuery: 'find issues with summary bug'")
    jira_service.search("find issues with summary bug")

    print("\nQuery: 'issues assigned to Alice'")
    jira_service.search("issues assigned to Alice")

    print("\nQuery: 'show issues in project Alpha'")
    jira_service.search("show issues in project Alpha")

    print("\nQuery: 'what issues are blocking ISSUE-1'")
    jira_service.search("what issues are blocking ISSUE-1")

    print("\nQuery: 'find all users'")
    jira_service.search("find all users")

    print("\nQuery: 'show all projects'")
    jira_service.search("show all projects")

    print("\nQuery: 'search for sprint planning'")
    jira_service.search("search for sprint planning")

    print("\nQuery: 'what did Alice do yesterday'")
    jira_service.search("what did Alice do yesterday")

    print("\nQuery: 'issues updated today'")
    jira_service.search("issues updated today")

    print("\nQuery: 'issues updated on 2025-06-29'")
    jira_service.search("issues updated on 2025-06-29")
