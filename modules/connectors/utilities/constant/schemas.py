from dataclasses import dataclass, field
from typing import List, Optional, Dict
from datetime import datetime


# -------------------------------
# Entity Schema
# -------------------------------

@dataclass
class Entity:
    """
    Represents an entity in the knowledge graph.
    """
    name: str
    entity_type: str
    description: Optional[str] = None
    aliases: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    properties: Optional[Dict] = None

    def __post_init__(self):
        if not self.name.strip():
            raise ValueError("Entity name cannot be empty")
        if not self.entity_type.strip():
            raise ValueError("Entity type cannot be empty")
        self.name = self.name.strip()
        self.entity_type = self.entity_type.strip()


# -------------------------------
# EntityRelationship Schema
# -------------------------------

@dataclass
class EntityRelationship:
    """
    Represents a relationship (edge) between two entities.
    """
    subject: str
    predicate: str
    object: str
    subject_type: Optional[str] = None
    object_type: Optional[str] = None
    confidence_score: Optional[float] = None
    context: Optional[str] = None
    source_sentence: Optional[str] = None
    source_file_id: Optional[str] = None
    org_context: Optional[Dict[str, str]] = None
    extraction_timestamp: Optional[datetime] = None

    def __post_init__(self):
        if not self.subject.strip():
            raise ValueError("Subject cannot be empty")
        if not self.predicate.strip():
            raise ValueError("Predicate cannot be empty")
        if not self.object.strip():
            raise ValueError("Object cannot be empty")
        self.subject = self.subject.strip()
        self.predicate = self.predicate.strip()
        self.object = self.object.strip()


# -------------------------------
# Document Summary
# -------------------------------

@dataclass
class DocumentSummary:
    """
    High-level extracted summary of a document.
    """
    title: str
    summary: str
    document_type: Optional[str] = None
    key_topics: Optional[List[str]] = None
    language: Optional[str] = None
    confidence_score: Optional[float] = None


# -------------------------------
# Aggregated Extracted Data
# -------------------------------

@dataclass
class ExtractedData:
    """
    Full extraction result: document summary, entities, and relationships.
    """
    document_summary: DocumentSummary
    entities: List[Entity]
    relationships: List[EntityRelationship]
    extraction_timestamp: Optional[datetime] = None
    extraction_model: Optional[str] = None
    processing_time_seconds: Optional[float] = None


# -------------------------------
# Chunk Data for Embeddings
# -------------------------------

@dataclass
class ChunkData:
    """
    Represents a chunk of text used for embedding or processing.
    """
    chunk_id: str
    text: str
    start_position: int
    end_position: int
    chunk_index: Optional[int] = None
    overlap_with_previous: Optional[int] = None
    overlap_with_next: Optional[int] = None
    embedding: Optional[List[float]] = None


# -------------------------------
# Graph Node Storage (Neo4j, etc.)
# -------------------------------

@dataclass
class GraphNode:
    """
    Represents a node in a KG backend (e.g., Neo4j).
    """
    node_id: str
    label: str
    name: str
    properties: Dict = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None


@dataclass
class GraphRelationship:
    """
    Represents a relationship edge in a KG backend (e.g., Neo4j).
    """
    relationship_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str
    properties: Dict = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None
    confidence_score: Optional[float] = None


# -------------------------------
# Vector Document for Embedding
# -------------------------------

@dataclass
class VectorDocument:
    """
    Represents a document chunk to store in a vector database.
    """
    document_id: str
    text: str
    embedding: List[float]
    metadata: Dict = field(default_factory=dict)

    def __post_init__(self):
        if not self.document_id.strip():
            raise ValueError("Document ID cannot be empty")
        if not self.text.strip():
            raise ValueError("Text cannot be empty")
        if not self.embedding:
            raise ValueError("Embedding cannot be empty")
