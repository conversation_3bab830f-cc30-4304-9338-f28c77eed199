from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


# -------------------------------
# Entity Schema
# -------------------------------

@dataclass
class Entity:
    """
    Represents an entity in the knowledge graph.
    """
    name: str
    entity_type: str
    description: Optional[str] = None
    aliases: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    properties: Optional[Dict] = None

    def __post_init__(self):
        if not self.name.strip():
            raise ValueError("Entity name cannot be empty")
        if not self.entity_type.strip():
            raise ValueError("Entity type cannot be empty")
        self.name = self.name.strip()
        self.entity_type = self.entity_type.strip()


# -------------------------------
# EntityRelationship Schema
# -------------------------------

@dataclass
class EntityRelationship:
    """
    Represents a relationship (edge) between two entities.
    """
    subject: str
    predicate: str
    object: str
    subject_type: Optional[str] = None
    object_type: Optional[str] = None
    confidence_score: Optional[float] = None
    context: Optional[str] = None
    source_sentence: Optional[str] = None
    source_file_id: Optional[str] = None
    org_context: Optional[Dict[str, str]] = None
    extraction_timestamp: Optional[datetime] = None

    def __post_init__(self):
        if not self.subject.strip():
            raise ValueError("Subject cannot be empty")
        if not self.predicate.strip():
            raise ValueError("Predicate cannot be empty")
        if not self.object.strip():
            raise ValueError("Object cannot be empty")
        self.subject = self.subject.strip()
        self.predicate = self.predicate.strip()
        self.object = self.object.strip()


# -------------------------------
# Document Summary
# -------------------------------

@dataclass
class DocumentSummary:
    """
    High-level extracted summary of a document.
    """
    title: str
    summary: str
    document_type: Optional[str] = None
    key_topics: Optional[List[str]] = None
    language: Optional[str] = None
    confidence_score: Optional[float] = None


# -------------------------------
# Aggregated Extracted Data
# -------------------------------

@dataclass
class ExtractedData:
    """
    Full extraction result: document summary, entities, and relationships.
    """
    document_summary: DocumentSummary
    entities: List[Entity]
    relationships: List[EntityRelationship]
    extraction_timestamp: Optional[datetime] = None
    extraction_model: Optional[str] = None
    processing_time_seconds: Optional[float] = None


# -------------------------------
# Chunk Data for Embeddings
# -------------------------------

@dataclass
class ChunkData:
    """
    Represents a chunk of text used for embedding or processing.
    """
    chunk_id: str
    text: str
    start_position: int
    end_position: int
    chunk_index: Optional[int] = None
    overlap_with_previous: Optional[int] = None
    overlap_with_next: Optional[int] = None
    embedding: Optional[List[float]] = None


# -------------------------------
# Graph Node Storage (Neo4j, etc.)
# -------------------------------

@dataclass
class GraphNode:
    """
    Represents a node in a KG backend (e.g., Neo4j).
    """
    node_id: str
    label: str
    name: str
    properties: Dict = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None


@dataclass
class GraphRelationship:
    """
    Represents a relationship edge in a KG backend (e.g., Neo4j).
    """
    relationship_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str
    properties: Dict = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None
    confidence_score: Optional[float] = None


# -------------------------------
# Vector Document for Embedding
# -------------------------------

@dataclass
class VectorDocument:
    """
    Represents a document chunk to store in a vector database.
    """
    document_id: str
    text: str
    embedding: List[float]
    metadata: Dict = field(default_factory=dict)

    def __post_init__(self):
        if not self.document_id.strip():
            raise ValueError("Document ID cannot be empty")
        if not self.text.strip():
            raise ValueError("Text cannot be empty")
        if not self.embedding:
            raise ValueError("Embedding cannot be empty")


# -------------------------------
# Standardized Search Response Schemas
# -------------------------------

class SearchStatus(Enum):
    """Status of search operation"""
    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"
    NO_RESULTS = "no_results"


class ConnectorType(Enum):
    """Types of connectors"""
    STRUCTURED = "structured"
    UNSTRUCTURED = "unstructured"


@dataclass
class SearchResultItem:
    """
    Individual search result item with standardized format
    """
    id: str
    title: str
    content: str
    source_type: str  # e.g., "jira", "confluence", "github"
    entity_type: str  # e.g., "JiraIssue", "ConfluencePage", "GitHubRepository"
    url: Optional[str] = None
    summary: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    relevance_score: Optional[float] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)

    def __post_init__(self):
        if not self.id.strip():
            raise ValueError("Search result ID cannot be empty")
        if not self.title.strip():
            raise ValueError("Search result title cannot be empty")
        if not self.source_type.strip():
            raise ValueError("Source type cannot be empty")
        if not self.entity_type.strip():
            raise ValueError("Entity type cannot be empty")


@dataclass
class SearchMetrics:
    """
    Performance and execution metrics for search operation
    """
    execution_time_ms: float
    total_results_found: int
    results_returned: int
    cache_hit: bool = False
    query_complexity_score: Optional[float] = None
    connector_response_time_ms: Optional[float] = None


@dataclass
class SearchError:
    """
    Error information for failed search operations
    """
    error_code: str
    error_message: str
    error_type: str  # e.g., "validation_error", "connection_error", "timeout_error"
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ConnectorSearchResponse:
    """
    Standardized response format for all connector search operations
    """
    status: SearchStatus
    query: str
    results: List[SearchResultItem] = field(default_factory=list)
    total_count: int = 0
    page: int = 1
    page_size: int = 10
    has_more: bool = False
    connector_info: Dict[str, str] = field(default_factory=dict)  # name, version, type
    metrics: Optional[SearchMetrics] = None
    error: Optional[SearchError] = None
    timestamp: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        if not self.query.strip():
            raise ValueError("Search query cannot be empty")

        # Ensure total_count matches results if not explicitly set
        if self.total_count == 0 and self.results:
            self.total_count = len(self.results)

        # Set connector info from results if not provided
        if not self.connector_info and self.results:
            first_result = self.results[0]
            self.connector_info = {
                "source_type": first_result.source_type,
                "entity_types": list(set(r.entity_type for r in self.results))
            }


@dataclass
class OrganizationSearchRequest:
    """
    Request format for organization-level search
    """
    query: str
    org_id: str
    user_id: Optional[str] = None
    filters: Dict[str, Any] = field(default_factory=dict)
    page: int = 1
    page_size: int = 10
    include_connectors: Optional[List[str]] = None  # Specific connectors to search
    exclude_connectors: Optional[List[str]] = None  # Connectors to exclude
    search_options: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        if not self.query.strip():
            raise ValueError("Search query cannot be empty")
        if not self.org_id.strip():
            raise ValueError("Organization ID cannot be empty")
        if self.page < 1:
            raise ValueError("Page number must be >= 1")
        if self.page_size < 1 or self.page_size > 100:
            raise ValueError("Page size must be between 1 and 100")


@dataclass
class OrganizationSearchResponse:
    """
    Aggregated response format for organization-level search across multiple connectors
    """
    status: SearchStatus
    request: OrganizationSearchRequest
    connector_responses: List[ConnectorSearchResponse] = field(default_factory=list)
    aggregated_results: List[SearchResultItem] = field(default_factory=list)
    total_results: int = 0
    connectors_searched: List[str] = field(default_factory=list)
    connectors_failed: List[str] = field(default_factory=list)
    overall_metrics: Optional[SearchMetrics] = None
    errors: List[SearchError] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

    def add_connector_response(self, response: ConnectorSearchResponse):
        """Add a connector response and update aggregated data"""
        self.connector_responses.append(response)

        if response.status == SearchStatus.SUCCESS:
            self.aggregated_results.extend(response.results)
            self.total_results += response.total_count
            if response.connector_info.get("source_type"):
                self.connectors_searched.append(response.connector_info["source_type"])
        else:
            if response.error and response.connector_info.get("source_type"):
                self.connectors_failed.append(response.connector_info["source_type"])
                self.errors.append(response.error)

    def finalize_response(self):
        """Finalize the response after all connector responses are added"""
        if self.aggregated_results:
            self.status = SearchStatus.SUCCESS
        elif self.errors:
            self.status = SearchStatus.ERROR if len(self.connectors_failed) == len(self.connector_responses) else SearchStatus.PARTIAL
        else:
            self.status = SearchStatus.NO_RESULTS

        # Sort results by relevance score if available
        self.aggregated_results.sort(key=lambda x: x.relevance_score or 0, reverse=True)
