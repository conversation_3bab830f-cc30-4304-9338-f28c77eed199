# Developer Guide: Adding a New Connector

This guide provides a complete walkthrough for creating and integrating a new connector into the system. Please follow these steps carefully to ensure your connector is compliant with our Unified Connector Architecture.

## 1. Introduction

Our connector architecture is designed to be modular, scalable, and easy to maintain. By following this guide, you will create a self-contained connector that can be seamlessly registered and used by the application.

## 2. Connector Types & Categories

Before you begin, you must identify the type and category of your connector.

*   **Connector Type:**
    *   `structured`: For data sources with a well-defined schema (e.g., APIs, databases).
    *   `unstructured`: For data sources with free-form content (e.g., documents, web pages).
*   **Category:** Choose one of the predefined categories that best fits your connector (e.g., `Task Management`, `Documentation`, `Code Repository`).

This information will be used in the `connector_info.json` file.

## 3. Step 1: Create the Directory Structure

All connectors reside in the `modules/connectors/handlers/` directory. Create a new directory for your connector (e.g., `my_connector`) with the following structure:

```
modules/connectors/handlers/
└── my_connector/
    ├── __init__.py
    ├── connection.py               # Handles connection logic and health checks.
    ├── service.py                  # Contains the main connector implementation.
    ├── constants/                  # Directory for node and relationship definitions
    │   ├── __init__.py
    │   ├── entities.py
    │   └── relationships.py
    ├── schema.py                   # Defines Pydantic schemas for data validation.
    ├── tests/
    │   └── test_my_connector.py    # Unit and integration tests.
    └── connector_info.json         # Metadata for the registration script.
```

## 4. Step 2: Implement the Connector Service

The core logic of your connector resides in the `service.py` file. Here, you will create a class that inherits from `BaseConnector`.

**`service.py` Implementation:**

1.  **Create the Class:** Define a class (e.g., `MyConnectorService`) that inherits from `BaseConnector`.
2.  **Set Connector Type:** Set the `CONNECTOR_TYPE` class variable to either `"structured"` or `"unstructured"`.
3.  **Implement Abstract Methods:** Provide concrete implementations for all abstract methods defined in the `BaseConnector` class.

```python
# In modules/connectors/handlers/my_connector/service.py

from modules.connectors.base import BaseConnector
from typing import Any, Dict, List, Iterator

class MyConnectorService(BaseConnector):
    """
    Service implementation for My Connector.
    """
    CONNECTOR_TYPE = "unstructured"

    def connect(self) -> Any:
        # Implementation here
        pass

    def get_connector(self) -> dict:
        # Implementation here
        pass

    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        # Implementation here
        pass

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        # Implementation here
        pass

    def sync(self):
        # Implementation here
        pass

    def sync_by_id(self, id: str):
        # Implementation here
        pass

    def store_context(self, data: Any):
        # Implementation here
        pass

    def search(self, query: str) -> List[Dict[str, Any]]:
        # Implementation here
        pass
```

## 5. Step 3: Define Nodes and Relationships

The registration script dynamically loads node and relationship definitions. Your responsibility is to create the files where these definitions live.

*   **For `unstructured` connectors:** This step is not required. The registration script automatically loads a standard set of definitions from `modules/connectors/utilities/constant/`.
*   **For `structured` connectors:** You must define the specific nodes and relationships for your connector inside the `constants` directory you created in Step 1.

**File Structure:**

```
<connector_name>/
└── constants/
    ├── __init__.py
    ├── entities.py
    └── relationships.py
```

**Implementation (`entities.py`):**

Create an `Enum` named `EntityType` and a function `get_all_entity_types()` that returns all values.

```python
# In <connector_name>/constants/entities.py
from enum import Enum

class EntityType(Enum):
    JIRA_PROJECT = "JiraProject"
    JIRA_TICKET = "JiraTicket"

def get_all_entity_types():
    return {e.value for e in EntityType}
```

**Implementation (`relationships.py`):**

Create an `Enum` named `RelationshipType` and a function `get_all_relationship_types()`.

```python
# In <connector_name>/constants/relationships.py
from enum import Enum

class RelationshipType(Enum):
    HAS_TICKET = "HAS_TICKET"
    ASSIGNED_TO = "ASSIGNED_TO"

def get_all_relationship_types():
    return {r.value for r in RelationshipType}
```

## 6. Step 4: Define Connector Metadata

Create a `connector_info.json` file in your connector's root directory. **Crucially, always leave the `nodes` and `relationships` fields as empty arrays.** They will be populated dynamically by the registration script.

**Example `connector_info.json`:**

```json
{
  "source_type": "my_connector",
  "name": "My Awesome Connector",
  "connector_type": "structured",
  "category": "Task Management",
  "icon": "base64_encoded_icon_string",
  "description": "A connector for the 'My Awesome Tool' platform.",
  "purpose": "To sync tasks and projects into the knowledge graph.",
  "nodes": [],
  "relationships": [],
  "example_usage": "Fetches all projects and their associated tasks.",
  "example_queries": [
    "MATCH (p:JiraProject)-[:HAS_TICKET]->(t:JiraTicket) RETURN p.name, t.summary"
  ]
}
```

## 7. Step 5: Register the Connector

Once your connector is implemented and the metadata is defined, you must register it using the provided CLI script.

Run the following command from the project's root directory:

```bash
python modules/connectors/register_connector.py --path modules/connectors/handlers/my_connector
```

This script will read your `connector_info.json`, validate it, and register the connector in the central registry (Postgres/Pinecone).

## 8. Step 6: Write Tests

Testing is a critical part of the development process. You must write unit and integration tests for your connector in the `tests/test_my_connector.py` file. Ensure you cover:

*   Connection logic (`connect`).
*   Data fetching (`fetch_data`, `fetch_data_by_id`).
*   Data processing and transformation logic.
*   Error handling.

By following this guide, you will create a high-quality, maintainable connector that integrates smoothly into our system.