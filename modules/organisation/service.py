"""
Organization Service Module

This module provides the main organization service that coordinates search operations
across multiple connectors and data sources within an organization.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from modules.connectors.utilities.constant.schemas import (
    OrganizationSearchRequest,
    OrganizationSearchResponse,
    ConnectorSearchResponse,
    SearchStatus,
    SearchMetrics,
    SearchError
)
# from modules.connectors.connector_factory import connector_factory
from modules.organisation.connector_router import ConnectorRouter

# Configure logging
logger = logging.getLogger(__name__)


class OrganizationService:
    """
    Main organization service that handles search operations across multiple connectors.
    
    This service acts as the central coordinator for all search operations within an organization,
    routing queries to appropriate connectors and aggregating results.
    """
    
    def __init__(self, org_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the organization service.
        
        Args:
            org_id: Organization identifier
            config: Optional configuration dictionary
        """
        self.org_id = org_id
        self.config = config or {}
        self.connector_router = ConnectorRouter(org_id, config)
        
        # Default search configuration
        self.default_search_config = {
            "max_results_per_connector": 50,
            "timeout_seconds": 30,
            "enable_parallel_search": True,
            "fallback_to_all_connectors": False
        }
        self.default_search_config.update(self.config.get("search", {}))
        
        logger.info(f"OrganizationService initialized for org_id: {org_id}")
    
    def search(self, query: str, **kwargs) -> OrganizationSearchResponse:
        """
        Main search method that coordinates search across multiple connectors.
        
        This method:
        1. Creates a standardized search request
        2. Determines which connectors to search based on query intent
        3. Routes search requests to appropriate connectors
        4. Aggregates and returns results in standardized format
        
        Args:
            query: Search query string
            **kwargs: Additional search parameters (user_id, filters, page, page_size, etc.)
            
        Returns:
            OrganizationSearchResponse: Aggregated search results from all connectors
        """
        start_time = datetime.now()
        
        try:
            # Create standardized search request
            search_request = self._create_search_request(query, **kwargs)
            logger.info(f"Processing search request for org {self.org_id}: '{query}'")
            
            # Initialize response
            response = OrganizationSearchResponse(
                status=SearchStatus.SUCCESS,
                request=search_request
            )
            
            # Determine which connectors to search
            target_connectors = self._determine_target_connectors(search_request)
            logger.info(f"Target connectors for search: {target_connectors}")
            
            # Execute search across connectors
            if self.default_search_config.get("enable_parallel_search", True):
                connector_responses = self._execute_parallel_search(search_request, target_connectors)
            else:
                connector_responses = self._execute_sequential_search(search_request, target_connectors)
            
            # Aggregate results
            for connector_response in connector_responses:
                response.add_connector_response(connector_response)
            
            # Finalize response
            response.finalize_response()
            
            # Calculate overall metrics
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            response.overall_metrics = SearchMetrics(
                execution_time_ms=execution_time,
                total_results_found=response.total_results,
                results_returned=len(response.aggregated_results),
                cache_hit=any(r.metrics and r.metrics.cache_hit for r in response.connector_responses if r.metrics)
            )
            
            logger.info(f"Search completed in {execution_time:.2f}ms, found {response.total_results} results across {len(response.connectors_searched)} connectors")
            return response
            
        except Exception as e:
            logger.error(f"Search failed for org {self.org_id}: {str(e)}", exc_info=True)
            
            # Return error response
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            error_response = OrganizationSearchResponse(
                status=SearchStatus.ERROR,
                request=self._create_search_request(query, **kwargs),
                errors=[SearchError(
                    error_code="ORGANIZATION_SEARCH_ERROR",
                    error_message=str(e),
                    error_type="internal_error"
                )],
                overall_metrics=SearchMetrics(
                    execution_time_ms=execution_time,
                    total_results_found=0,
                    results_returned=0
                )
            )
            return error_response
    
    def _create_search_request(self, query: str, **kwargs) -> OrganizationSearchRequest:
        """Create a standardized search request from parameters."""
        return OrganizationSearchRequest(
            query=query,
            org_id=self.org_id,
            user_id=kwargs.get("user_id"),
            filters=kwargs.get("filters", {}),
            page=kwargs.get("page", 1),
            page_size=kwargs.get("page_size", 10),
            include_connectors=kwargs.get("include_connectors"),
            exclude_connectors=kwargs.get("exclude_connectors"),
            search_options=kwargs.get("search_options", {})
        )
    
    def _determine_target_connectors(self, request: OrganizationSearchRequest) -> List[str]:
        """
        Determine which connectors to search based on the request.
        
        Args:
            request: The search request
            
        Returns:
            List of connector names to search
        """
        return self.connector_router.determine_connectors(request)
    
    def _execute_parallel_search(self, request: OrganizationSearchRequest, connectors: List[str]) -> List[ConnectorSearchResponse]:
        """Execute search across multiple connectors in parallel."""
        # For now, implement sequential search
        # TODO: Implement actual parallel execution using threading or asyncio
        logger.info("Parallel search not yet implemented, falling back to sequential")
        return self._execute_sequential_search(request, connectors)
    
    def _execute_sequential_search(self, request: OrganizationSearchRequest, connectors: List[str]) -> List[ConnectorSearchResponse]:
        """Execute search across multiple connectors sequentially."""
        responses = []
        
        for connector_name in connectors:
            try:
                logger.info(f"Searching connector: {connector_name}")
                response = self._search_single_connector(request, connector_name)
                responses.append(response)
                
            except Exception as e:
                logger.error(f"Search failed for connector {connector_name}: {str(e)}")
                # Create error response for failed connector
                error_response = ConnectorSearchResponse(
                    status=SearchStatus.ERROR,
                    query=request.query,
                    connector_info={"source_type": connector_name},
                    error=SearchError(
                        error_code="CONNECTOR_SEARCH_ERROR",
                        error_message=str(e),
                        error_type="connector_error"
                    )
                )
                responses.append(error_response)
        
        return responses
    
    def _search_single_connector(self, request: OrganizationSearchRequest, connector_name: str) -> ConnectorSearchResponse:
        """
        Search a single connector and return standardized response.
        
        Args:
            request: The search request
            connector_name: Name of the connector to search
            
        Returns:
            ConnectorSearchResponse: Standardized response from the connector
        """
        # Get connector configuration for this organization
        connector_config = self._get_connector_config(connector_name)
        
        # Get connector instance (simplified for testing)
        if connector_name.lower() == "jira":
            from modules.connectors.handlers.Jira.service import JiraConnectorService
            connector_instance = JiraConnectorService(config=connector_config)
        else:
            raise ValueError(f"Connector '{connector_name}' not supported in test mode")

        # Execute search on the connector
        # The connector's search method should return a ConnectorSearchResponse
        return connector_instance.search(request.query)
    
    def _get_connector_config(self, connector_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific connector for this organization.
        
        Args:
            connector_name: Name of the connector
            
        Returns:
            Configuration dictionary for the connector
        """
        # TODO: Implement actual configuration retrieval from database
        # For now, return placeholder configuration
        
        if connector_name.lower() == "jira":
            return {
                "jira_url": "https://your-org.atlassian.net",
                "username": "api_user",
                "password": "api_token",
                "max_results": self.default_search_config.get("max_results_per_connector", 50),
                "enable_caching": True,
                "enable_query_logging": True
            }
        
        # Default configuration for other connectors
        return {
            "max_results": self.default_search_config.get("max_results_per_connector", 50),
            "timeout_seconds": self.default_search_config.get("timeout_seconds", 30)
        }
    
    def get_available_connectors(self) -> List[Dict[str, Any]]:
        """
        Get list of available connectors for this organization.
        
        Returns:
            List of connector metadata dictionaries
        """
        # TODO: Implement actual connector discovery from database
        return [
            {
                "name": "jira",
                "type": "structured",
                "category": "task_management",
                "status": "active",
                "description": "Jira issue tracking system"
            }
        ]
    
    def add_connector(self, connector_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new connector to this organization.
        
        Args:
            connector_type: Type of connector to add
            config: Configuration for the connector
            
        Returns:
            Result of the add operation
        """
        # TODO: Implement connector addition logic
        logger.info(f"Adding connector {connector_type} to organization {self.org_id}")
        return {"status": "success", "connector_type": connector_type}
